<div class="card auth-card">
  <div class="text-center mb-4">
    <h2 class="fw-bold text-dark">Reset Your Password</h2>
    <p class="text-muted">
      Enter your email address and we'll send you a reset code
    </p>
  </div>

  <form
    class="auth-form"
    [formGroup]="forgotPasswordForm"
    (ngSubmit)="onSubmit()"
  >
    <!-- Email Field -->
    <div class="mb-4">
      <label for="email" class="auth-input-label text-dark fw-medium"
        >Email Address</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-envelope auth-input-icon"></i>
        <input
          pInputText
          id="email"
          type="email"
          formControlName="email"
          class="auth-input py-2"
          placeholder="Enter your email"
          [class.error]="emailControl?.invalid && emailControl?.touched"
        />
      </p-inputGroup>
      <div
        *ngIf="emailControl?.hasError('required') && emailControl?.touched"
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Email is required
      </div>
      <div
        *ngIf="emailControl?.hasError('email') && emailControl?.touched"
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Please enter a valid email address
      </div>
    </div>

    <!-- Send Reset Code Button -->
    <div class="d-grid mb-3">
      <button
        pButton
        pRipple
        type="submit"
        [label]="isLoading ? 'Sending...' : 'Send Reset Code'"
        class="p-button-primary py-2 fw-bold"
        [disabled]="isLoading"
        [loading]="isLoading"
      ></button>
    </div>

    <!-- Back to Login -->
    <div class="text-center">
      <button
        pButton
        pRipple
        type="button"
        label="Back to Login"
        class="p-button-text text-decoration-none text-secondary p-0"
        (click)="onBackToLogin()"
      ></button>
    </div>
  </form>
</div>
