import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private readonly tokenKey = 'TOKEN';
  private readonly USER_INFO_KEY = 'USER_INFO';

  constructor() {}

  setToken(token: string): void {
    localStorage.setItem(this.tokenKey, token);
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  getUserInfo(): any {
    const userInfo = localStorage.getItem(this.USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
  }

  clearSession(): void {
    localStorage.removeItem(this.USER_INFO_KEY);
    localStorage.removeItem(this.tokenKey);
  }

  setUserInfo(payload: any): void {
    const mappedUserInfo = {
      id: payload.id || payload.nameid || '',
      name: payload.FullName || payload.name || '',
      email: payload.Email || payload.email || '',
      role: payload.role || [],
      isActive: payload.IsActive ?? true,
      phoneNumber: payload.PhoneNumber,
      description: payload.Description,
      profilePictureUrl: payload.ProfilePictureUrl,
    };

    localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(mappedUserInfo));
  }

  decodeToken(): any | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      return jwtDecode(token);
    } catch (error) {
      console.error('Invalid token', error);
      return null;
    }
  }

  isTokenExpired(): boolean {
    const decoded = this.decodeToken();
    if (!decoded || !decoded.exp) return true;

    const expiry = decoded.exp * 1000;
    return Date.now() > expiry;
  }

  // private setUserSession(user: Auth): void {
  //   if (user.token) {
  //     const data = {
  //       name: user.name,
  //       email: user.email,
  //       token: user.token,
  //       userId: user.userId,
  //     };
  //     this.authToken = user.token;
  //     this.setStorageItem(this.TOKEN_KEY, user.token);
  //     this.setStorageItem(this.USER_DATA_KEY, JSON.stringify(data));
  //     this.setSessionExpiry();
  //     this.isAuthenticated = true;

  //     if (user.role === undefined) {
  //       const redirectUrl = this.getRedirectUrl();
  //       this.setRedirectUrl(redirectUrl || '/user');
  //     }
  //   } else {
  //     console.error('User token is undefined');
  //     this.isAuthenticated = false;
  //   }
  // }

  // private setStorageItem(key: string, value: string): void {
  //   localStorage.setItem(key, value);
  //   sessionStorage.setItem(key, value);
  // }

  // private removeStorageItem(key: string): void {
  //   localStorage.removeItem(key);
  //   sessionStorage.removeItem(key);
  // }

  // private setSessionExpiry(): void {
  //   const expiryTime = new Date().getTime() + this.SESSION_DURATION;
  //   sessionStorage.setItem(this.SESSION_EXPIRY_KEY, expiryTime.toString());
  // }

  // private checkSessionExpiry(): void {
  //   const expiryTime = sessionStorage.getItem(this.SESSION_EXPIRY_KEY);
  //   if (expiryTime) {
  //     const currentTime = new Date().getTime();
  //     if (currentTime >= +expiryTime) {
  //       this.logout();
  //     }
  //   }
  // }

  // isLoggedIn(): boolean {
  //   this.checkSessionExpiry();
  //   this.authToken = this.getToken();
  //   return !!this.authToken;
  // }

  // getToken(): string | null {
  //   return sessionStorage.getItem(this.TOKEN_KEY) || localStorage.getItem(this.TOKEN_KEY);
  // }

  // logout(): void {
  //   this.removeStorageItem(this.TOKEN_KEY);
  //   this.removeStorageItem(this.USER_DATA_KEY);
  //   sessionStorage.removeItem(this.SESSION_EXPIRY_KEY);
  //   this.clearSessionStorage();
  //   this.isAuthenticated = false;
  //   this.authToken = null;
  //   this.router.navigate(['/login']);
  // }

  // private clearSessionStorage(): void {
  //   sessionStorage.clear();
  // }
}
