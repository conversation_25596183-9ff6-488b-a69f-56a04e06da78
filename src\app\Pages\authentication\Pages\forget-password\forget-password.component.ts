import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../Services/Auth.service';
import { NotificationService } from '../../../../core/Services/Notification.service';
import { ForgotPasswordRequest } from '../../models/RequestTypes';

@Component({
  selector: 'app-forget-password',
  standalone: false,
  templateUrl: './forget-password.component.html',
  styleUrl: './forget-password.component.scss',
})
export class ForgetPasswordComponent implements OnInit {
  forgotPasswordForm!: FormGroup;
  isLoading = false;

  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private notification = inject(NotificationService);

  ngOnInit(): void {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordForm.invalid) {
      this.forgotPasswordForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    const request: ForgotPasswordRequest = {
      email: this.forgotPasswordForm.value.email,
    };

    this.authService.forgotPassword(request).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notification.showSuccess(
            'Reset code sent to your email successfully!',
          );
          this.router.navigate(['/auth/verify-reset-token'], {
            queryParams: { email: request.email },
          });
        }
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  get emailControl() {
    return this.forgotPasswordForm.get('email');
  }
}
