<div class="card auth-card">
  <div class="text-center mb-4">
    <h2 class="fw-bold text-dark">Verify Your Identity</h2>
    <p class="text-muted">
      Enter the 5-digit verification code sent to your device
    </p>
  </div>

  <form class="auth-form" [formGroup]="otpForm" (ngSubmit)="onVerifyOtp()">
    <!-- OTP Input Field -->
    <div class="mb-4">
      <label for="otp" class="auth-input-label text-dark fw-medium"
        >Verification Code</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-shield auth-input-icon"></i>
        <input
          pInputText
          id="otp"
          type="text"
          formControlName="otp"
          class="auth-input py-2"
          placeholder="Enter 5-digit code"
          maxlength="5"
          [class.error]="
            otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched
          "
        />
      </p-inputGroup>
      <div
        *ngIf="
          otpForm.get('otp')?.hasError('required') &&
          otpForm.get('otp')?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Verification code is required
      </div>
      <div
        *ngIf="
          otpForm.get('otp')?.hasError('pattern') && otpForm.get('otp')?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Please enter a valid 5-digit code
      </div>
    </div>

    <!-- Verify Button -->
    <div class="d-grid mb-3">
      <button
        pButton
        pRipple
        type="submit"
        [label]="isLoading ? 'Verifying...' : 'Verify Code'"
        class="p-button-primary py-2 fw-bold"
        [disabled]="isLoading"
        [loading]="isLoading"
      ></button>
    </div>

    <!-- Resend Section -->
    <div class="text-center mb-3">
      <p class="text-muted mb-2">Didn't receive the code?</p>
      <button
        pButton
        pRipple
        type="button"
        [label]="canResend ? 'Resend Code' : `Resend in ${timerDisplay}`"
        class="p-button-text text-decoration-none text-primary p-0"
        [disabled]="!canResend"
        (click)="onResendOtp()"
      ></button>
    </div>

    <!-- Back to Login -->
    <div class="text-center">
      <button
        pButton
        pRipple
        type="button"
        label="Back to Login"
        class="p-button-text text-decoration-none text-secondary p-0"
        (click)="onBackToLogin()"
      ></button>
    </div>
  </form>
</div>
