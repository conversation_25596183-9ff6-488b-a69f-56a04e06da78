import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../Services/Auth.service';
import { NotificationService } from '../../../../core/Services/Notification.service';
import { Subject, Subscription, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ForgotPasswordRequest } from '../../models/RequestTypes';

@Component({
  selector: 'app-verify-reset-token',
  standalone: false,
  templateUrl: './verify-reset-token.component.html',
  styleUrl: './verify-reset-token.component.scss',
})
export class VerifyResetTokenComponent implements OnInit, OnDestroy {
  verifyTokenForm!: FormGroup;
  isLoading = false;
  email: string = '';

  // Timer state
  resendTimer = 0;
  canResend = false;
  private timerSubscription?: Subscription;
  destroy$ = new Subject<void>();

  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private authService = inject(AuthService);
  private notification = inject(NotificationService);

  ngOnInit(): void {
    this.verifyTokenForm = this.fb.group({
      token: [
        '',
        [
          Validators.required,
          Validators.pattern(/^\d{6}$/),
          Validators.minLength(6),
          Validators.maxLength(6),
        ],
      ],
    });

    this.route.queryParams.subscribe((params) => {
      this.email = params['email'];
      if (!this.email) {
        this.router.navigate(['/auth/forgot-password']);
        return;
      }
      this.startResendTimer();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  onVerifyToken(): void {
    if (this.verifyTokenForm.invalid) {
      this.verifyTokenForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    const token = this.verifyTokenForm.value.token;

    this.authService
      .verifyResetToken({ email: this.email, resetToken: token })
      .subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.notification.showSuccess('Reset code verified successfully!');
            this.router.navigate(['/auth/reset-password'], {
              queryParams: {
                email: this.email,
                token: token,
              },
            });
          } else {
            this.isLoading = false;
            this.clearToken();
          }
        },
        error: () => {
          this.isLoading = false;
          this.clearToken();
        },
      });
  }

  onResendCode(): void {
    if (!this.canResend) return;

    this.isLoading = true;

    this.authService.forgotPassword({ email: this.email }).subscribe({
      next: () => {
        this.notification.showSuccess('Reset code sent successfully!');
        this.startResendTimer();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  clearToken(): void {
    this.verifyTokenForm.get('token')?.setValue('');
    this.verifyTokenForm.get('token')?.markAsUntouched();
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  get tokenControl() {
    return this.verifyTokenForm.get('token');
  }
}
