import { Injectable } from '@angular/core';
import {
  HttpRe<PERSON>,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { filter, take, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';

import { TokenStorageService } from '../../Pages/authentication/Services/TokenStorage.service';
import { API_URL_PATTERNS } from '../Utils/Constants';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null,
  );

  constructor(
    private router: Router,
    private tokenStorage: TokenStorageService,
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON><PERSON><PERSON>,
  ): Observable<HttpEvent<unknown>> {
    const token = this.tokenStorage.getToken();
    const isApiUrl = this.isApiRequest(request.url);

    if (token && isApiUrl) {
      request = this.addTokenHeader(request, token);
    }

    return next.handle(request);
  }

  private addTokenHeader(
    request: HttpRequest<any>,
    token: string,
  ): HttpRequest<any> {
    return request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`),
    });
  }

  private isApiRequest(url: string): boolean {
    return API_URL_PATTERNS.some(
      (pattern) => url.startsWith(pattern) || url.includes(pattern),
    );
  }
}
