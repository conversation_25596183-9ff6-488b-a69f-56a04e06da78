/* Sidebar Container */
.sidebar-container {
  width: 280px;
  height: 100vh;
  background: var(--bs-body-bg);
  border-right: 1px solid var(--bs-border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 1rem;
  box-sizing: border-box;
  position: relative;
  transition: width 0.3s ease;

  &.collapsed {
    width: 70px;
    padding: 0.5rem;
  }
}

/* Sidebar Toggle Button */
.sidebar-toggle,
.expand-toggle {
  background: transparent;
  border: none;
  color: var(--bs-body-color);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--bs-light);
  }

  .pi {
    font-size: 1.25rem;
  }
}

.expand-toggle {
  margin: 0 auto;
}

/* Header Section */
.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
  transition: all 0.3s ease;

  .collapsed & {
    justify-content: center;
    margin-bottom: 1rem;
  }
}

/* App Branding */
.app-branding {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.app-logo {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-fallback {
  width: 40px;
  height: 40px;
  background: #000000;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
}

.app-name {
  color: var(--bs-body-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
}

/* Logout Section */
.logout-section {
  transition: all 0.3s ease;
}

/* Navigation Section */
.navigation-section {
  flex: 1;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  overflow-y: auto;

  .collapsed & {
    margin-bottom: 1rem;
  }
}

/* Navigation Items */
.nav-item {
  border-radius: 0.5rem;
  margin-bottom: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--bs-secondary-color);
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  border-left: 3px solid transparent;

  .collapsed & {
    justify-content: center;
    padding: 0;
    border-left: none;
  }

  &:hover:not(.active) {
    background: var(--bs-light);
    color: var(--bs-body-color);

    .nav-icon {
      color: var(--bs-body-color);
    }
  }

  &.active {
    background: var(--bs-light) !important;
    color: var(--bs-body-color) !important;
    border-left: 3px solid var(--bs-primary) !important;

    .collapsed & {
      border-left: none !important;
      background: var(--bs-light) !important;
    }

    .nav-icon {
      color: var(--bs-body-color) !important;
    }

    .nav-label {
      color: var(--bs-body-color) !important;
      font-weight: 600 !important;
    }
  }
}

.nav-icon {
  color: var(--bs-secondary-color);
  margin-right: 0.5rem;
  transition: color 0.2s ease;
  flex-shrink: 0;
  font-size: 1.25rem;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .collapsed & {
    margin-right: 0;
  }
}

.nav-label {
  color: var(--bs-body-color);
  font-weight: 500;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.3s ease;
}

.nav-badge {
  background: var(--bs-primary);
  color: var(--bs-white);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  margin-left: auto;
}

/* Settings Section */
.settings-section {
  margin-top: auto;
  border-top: 1px solid var(--bs-border-color);
  transition: all 0.3s ease;

  .collapsed & {
    padding: 0.5rem 0;
  }
}

/* Theme Toggle */
.theme-toggle {
  margin-left: auto;
}

:host-context(.p-inputswitch-checked) .p-inputswitch-slider {
  background-color: var(--bs-primary) !important;
}

:host-context(.p-inputswitch-checked) .p-inputswitch-slider::before {
  background-color: var(--bs-white) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-container {
    width: 100%;
    height: auto;
    min-height: 100vh;

    &.collapsed {
      width: 100%;
    }
  }

  .sidebar-toggle,
  .expand-toggle {
    display: none;
  }

  .logout-section {
    padding: 1rem;
  }

  .app-name {
    font-size: 1.25rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .sidebar-container {
    background: #ffffff;
    border-right-color: #dee2e6;
  }

  .sidebar-toggle,
  .expand-toggle {
    color: #333333;

    &:hover {
      background: #f8f9fa;
    }
  }

  .nav-item {
    color: #6c757d;

    &:hover:not(.active) {
      background: #f8f9fa;
      color: #333333;

      .nav-icon {
        color: #333333;
      }
    }

    &.active {
      background: #f8f9fa !important;
      color: #333333 !important;
      border-left: 3px solid var(--bs-primary) !important;

      .collapsed & {
        border-left: none !important;
      }

      .nav-icon {
        color: #333333 !important;
      }

      .nav-label {
        color: #333333 !important;
        font-weight: 600 !important;
      }
    }
  }

  .app-name {
    color: #333333;
  }
}

/* Dark Theme Overrides */
:host-context(.dark-theme) {
  .sidebar-container {
    background: #212529;
    border-right-color: #495057;
  }

  .sidebar-toggle,
  .expand-toggle {
    color: #ffffff;

    &:hover {
      background: #343a40;
    }
  }

  .nav-item {
    color: #adb5bd;

    &:hover:not(.active) {
      background: #343a40;
      color: #ffffff;

      .nav-icon {
        color: #ffffff;
      }
    }

    &.active {
      background: #343a40 !important;
      color: #ffffff !important;
      border-left: 3px solid var(--bs-primary) !important;

      .collapsed & {
        border-left: none !important;
      }

      .nav-icon {
        color: #ffffff !important;
      }

      .nav-label {
        color: #ffffff !important;
        font-weight: 600 !important;
      }
    }
  }

  .app-name {
    color: #ffffff;
  }
}
