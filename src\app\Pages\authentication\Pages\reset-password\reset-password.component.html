<div class="card auth-card">
  <div class="text-center mb-4">
    <h2 class="fw-bold text-dark">Reset Your Password</h2>
    <p class="text-muted">Enter your new password below</p>
  </div>

  <form
    class="auth-form"
    [formGroup]="resetPasswordForm"
    (ngSubmit)="onSubmit()"
  >
    <!-- New Password Field -->
    <div class="mb-3">
      <label for="newPassword" class="auth-input-label text-dark fw-medium"
        >New Password</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-lock auth-input-icon"></i>
        <input
          [type]="hideNewPassword ? 'password' : 'text'"
          pInputText
          id="newPassword"
          formControlName="newPassword"
          class="auth-input py-2"
          placeholder="Enter new password"
          [class.error]="
            newPasswordControl?.invalid && newPasswordControl?.touched
          "
        />
        <button
          type="button"
          (click)="toggleNewPasswordVisibility()"
          class="auth-password-toggle"
          [attr.aria-label]="'Toggle password visibility'"
          [attr.aria-pressed]="!hideNewPassword"
        >
          <i class="pi pi-eye-slash" [class.pi-eye]="!hideNewPassword"></i>
        </button>
      </p-inputGroup>
      <div
        *ngIf="
          newPasswordControl?.hasError('required') &&
          newPasswordControl?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        New password is required
      </div>
      <div
        *ngIf="
          newPasswordControl?.hasError('minlength') &&
          newPasswordControl?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Password must be at least 8 characters long
      </div>
    </div>

    <!-- Confirm Password Field -->
    <div class="mb-4">
      <label for="confirmPassword" class="auth-input-label text-dark fw-medium"
        >Confirm Password</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-lock auth-input-icon"></i>
        <input
          [type]="hideConfirmPassword ? 'password' : 'text'"
          pInputText
          id="confirmPassword"
          formControlName="confirmPassword"
          class="auth-input py-2"
          placeholder="Confirm new password"
          [class.error]="
            confirmPasswordControl?.invalid && confirmPasswordControl?.touched
          "
        />
        <button
          type="button"
          (click)="toggleConfirmPasswordVisibility()"
          class="auth-password-toggle"
          [attr.aria-label]="'Toggle password visibility'"
          [attr.aria-pressed]="!hideConfirmPassword"
        >
          <i class="pi pi-eye-slash" [class.pi-eye]="!hideConfirmPassword"></i>
        </button>
      </p-inputGroup>
      <div
        *ngIf="
          confirmPasswordControl?.hasError('required') &&
          confirmPasswordControl?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Please confirm your password
      </div>
      <div
        *ngIf="
          resetPasswordForm?.hasError('passwordMismatch') &&
          confirmPasswordControl?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Passwords do not match
      </div>
    </div>

    <!-- Reset Password Button -->
    <div class="d-grid mb-3">
      <button
        pButton
        pRipple
        type="submit"
        [label]="isLoading ? 'Resetting...' : 'Reset Password'"
        class="p-button-primary py-2 fw-bold"
        [disabled]="isLoading"
        [loading]="isLoading"
      ></button>
    </div>

    <!-- Back to Login -->
    <div class="text-center">
      <button
        pButton
        pRipple
        type="button"
        label="Back to Login"
        class="p-button-text text-decoration-none text-secondary p-0"
        (click)="onBackToLogin()"
      ></button>
    </div>
  </form>
</div>
