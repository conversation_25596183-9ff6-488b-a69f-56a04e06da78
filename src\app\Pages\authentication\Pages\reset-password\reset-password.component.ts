import { Component, inject, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../Services/Auth.service';
import { NotificationService } from '../../../../core/Services/Notification.service';
import { ResetPasswordRequest } from '../../models/RequestTypes';

@Component({
  selector: 'app-reset-password',
  standalone: false,
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss',
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm!: FormGroup;
  isLoading = false;
  hideNewPassword = true;
  hideConfirmPassword = true;
  email: string = '';
  token: string = '';

  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private authService = inject(AuthService);
  private notification = inject(NotificationService);

  ngOnInit(): void {
    this.resetPasswordForm = this.fb.group(
      {
        newPassword: ['', [Validators.required, Validators.minLength(8)]],
        confirmPassword: ['', [Validators.required]],
      },
      { validators: this.passwordMatchValidator },
    );

    this.route.queryParams.subscribe((params) => {
      this.email = params['email'];
      this.token = params['token'];
      if (!this.email || !this.token) {
        this.router.navigate(['/auth/forgot-password']);
        return;
      }
    });
  }

  passwordMatchValidator(control: AbstractControl): ValidationErrors | null {
    const newPassword = control.get('newPassword');
    const confirmPassword = control.get('confirmPassword');

    if (
      newPassword &&
      confirmPassword &&
      newPassword.value !== confirmPassword.value
    ) {
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmit(): void {
    if (this.resetPasswordForm.invalid) {
      this.resetPasswordForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    const formValue = this.resetPasswordForm.value;
    const request: ResetPasswordRequest = {
      email: this.email,
      resetToken: this.token,
      newPassword: formValue.newPassword,
      confirmPassword: formValue.confirmPassword,
    };

    this.authService.resetPassword(request).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notification.showSuccess(
            'Password reset successfully! Please login with your new password.',
          );
          this.router.navigate(['/auth/login']);
        }
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  toggleNewPasswordVisibility(): void {
    this.hideNewPassword = !this.hideNewPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  get newPasswordControl() {
    return this.resetPasswordForm.get('newPassword');
  }

  get confirmPasswordControl() {
    return this.resetPasswordForm.get('confirmPassword');
  }
}
