import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './Pages/login/login.component';
import { OtpVerificaitonComponent } from './Pages/otp-verificaiton/otp-verificaiton.component';
import { ForgetPasswordComponent } from './Pages/forget-password/forget-password.component';
import { VerifyResetTokenComponent } from './Pages/verify-reset-token/verify-reset-token.component';
import { ResetPasswordComponent } from './Pages/reset-password/reset-password.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'login',
    component: LoginComponent,
  },
  {
    path: 'otp-verification',
    component: OtpVerificaitonComponent,
  },
  {
    path: 'forgot-password',
    component: ForgetPasswordComponent,
  },
  {
    path: 'verify-reset-token',
    component: VerifyResetTokenComponent,
  },
  {
    path: 'reset-password',
    component: ResetPasswordComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuthenticationRoutingModule {}
