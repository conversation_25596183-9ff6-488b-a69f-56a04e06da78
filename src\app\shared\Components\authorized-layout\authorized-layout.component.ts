import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthService } from '../../../Pages/authentication/Services/Auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-authorized-layout',
  standalone: false,
  templateUrl: './authorized-layout.component.html',
  styleUrl: './authorized-layout.component.scss',
})
export class AuthorizedLayoutComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  private authSubscription: Subscription = new Subscription();

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authSubscription = this.authService.isAuthenticated$.subscribe(
      (isAuth) => {
        this.isAuthenticated = isAuth;
      },
    );
  }

  ngOnDestroy(): void {
    this.authSubscription.unsubscribe();
  }
}
