<div class="card auth-card">
  <div class="text-center mb-4">
    <h2 class="fw-bold text-dark">Verify Reset Code</h2>
    <p class="text-muted">Enter the 6-digit code sent to your email</p>
  </div>

  <form
    class="auth-form"
    [formGroup]="verifyTokenForm"
    (ngSubmit)="onVerifyToken()"
  >
    <!-- Token Input Field -->
    <div class="mb-4">
      <label for="token" class="auth-input-label text-dark fw-medium"
        >Reset Code</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-key auth-input-icon"></i>
        <input
          pInputText
          id="token"
          type="text"
          formControlName="token"
          class="auth-input py-2"
          placeholder="Enter 6-digit code"
          maxlength="6"
          [class.error]="tokenControl?.invalid && tokenControl?.touched"
        />
      </p-inputGroup>
      <div
        *ngIf="tokenControl?.hasError('required') && tokenControl?.touched"
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Reset code is required
      </div>
      <div
        *ngIf="tokenControl?.hasError('pattern') && tokenControl?.touched"
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Please enter a valid 6-digit code
      </div>
    </div>

    <!-- Verify Button -->
    <div class="d-grid mb-3">
      <button
        pButton
        pRipple
        type="submit"
        [label]="isLoading ? 'Verifying...' : 'Verify Code'"
        class="p-button-primary py-2 fw-bold"
        [disabled]="isLoading"
        [loading]="isLoading"
      ></button>
    </div>

    <!-- Resend Section -->
    <div class="text-center mb-3">
      <p class="text-muted mb-2">Didn't receive the code?</p>
      <button
        pButton
        pRipple
        type="button"
        [label]="canResend ? 'Resend Code' : `Resend in ${timerDisplay}`"
        class="p-button-text text-decoration-none text-primary p-0"
        [disabled]="!canResend"
        (click)="onResendCode()"
      ></button>
    </div>

    <!-- Back to Login -->
    <div class="text-center">
      <button
        pButton
        pRipple
        type="button"
        label="Back to Login"
        class="p-button-text text-decoration-none text-secondary p-0"
        (click)="onBackToLogin()"
      ></button>
    </div>
  </form>
</div>
