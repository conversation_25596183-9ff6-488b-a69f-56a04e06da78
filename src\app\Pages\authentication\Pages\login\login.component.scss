@use "../../../../shared/scss/shared-auth-theme.scss" as *;

.auth-card {
  width: 500px;
  @extend .auth-card;
}

.auth-form {
  @extend .auth-form;
}

.auth-input {
  @extend .auth-input;
  border-radius: 8px !important;
}

.auth-input-label {
  @extend .auth-input-label;
}

.auth-input-icon {
  @extend .auth-input-icon;
}

// .p-inputtext {
//   border: none !important;
//   background: transparent !important;
//   padding-left: 0.5rem !important;
// }

// .p-inputgroup-addon {
//   background: transparent !important;
//   border: none !important;
//   border-bottom: 1px solid #ced4da !important;
//   border-radius: 0 !important;
// }

.p-button {
  border-radius: 8px !important;
}

// .p-button-text {
//   font-size: 0.9rem;
// }
