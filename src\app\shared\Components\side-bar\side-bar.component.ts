import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { AuthService } from '../../../Pages/authentication/Services/Auth.service';
import { DOCUMENT } from '@angular/common';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  action?: () => void;
  requiresAdmin?: boolean;
  badge?: number;
}

@Component({
  selector: 'app-side-bar',
  standalone: false,
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.scss'],
})
export class SideBarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  #document = inject(DOCUMENT);
  isDarkMode = false;

  // User roles for admin access
  userRoles: string[] = [];

  // Sidebar state
  isCollapsed = false;
  private readonly SIDEBAR_STATE_KEY = 'chat-app-sidebar-collapsed';

  // Navigation items
  navigationItems: NavigationItem[] = [
    {
      label: 'Messages',
      icon: 'pi-comment',
      route: '/dashboard',
      badge: 0,
    },
    {
      label: 'User Management',
      icon: 'pi-users',
      route: '/user-management',
      requiresAdmin: true,
    },
  ];

  constructor(
    private authService: AuthService,
    public router: Router,
  ) {}

  ngOnInit(): void {
    this.loadSidebarState();
    this.loadUserRoles();
    this.initializeTheme();
  }

  private loadUserRoles(): void {
    this.userRoles = this.authService.getUserRoles();
  }

  private initializeTheme(): void {
    const linkElement = this.#document.getElementById(
      'app-theme',
    ) as HTMLLinkElement;
    if (linkElement) {
      this.isDarkMode = linkElement.href.includes('dark');
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  toggleTheme(): void {
    const linkElement = this.#document.getElementById(
      'app-theme',
    ) as HTMLLinkElement;
    if (linkElement) {
      if (linkElement.href.includes('light')) {
        linkElement.href = 'theme-dark.css';
        this.isDarkMode = true;
      } else {
        linkElement.href = 'theme-light.css';
        this.isDarkMode = false;
      }
    }
  }

  onThemeToggleChange(event: any): void {
    this.toggleTheme();
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.saveSidebarState();
  }

  private loadSidebarState(): void {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_STATE_KEY);
      if (saved !== null) {
        this.isCollapsed = JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load sidebar state:', error);
    }
  }

  private saveSidebarState(): void {
    try {
      localStorage.setItem(
        this.SIDEBAR_STATE_KEY,
        JSON.stringify(this.isCollapsed),
      );
    } catch (error) {
      console.warn('Failed to save sidebar state:', error);
    }
  }

  onLogout(): void {
    // For now, directly logout. We can add confirmation dialog later if needed
    this.authService.logout();
  }

  shouldShowNavItem(item: NavigationItem): boolean {
    if (item.requiresAdmin) {
      const hasOnlyUserRole =
        this.userRoles.length === 1 &&
        this.userRoles[0].toLowerCase() === 'user';

      if (hasOnlyUserRole) {
        return false;
      }

      const hasAdminRole = this.userRoles.some(
        (role) =>
          role.toLowerCase() === 'admin' ||
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return hasAdminRole;
    }
    return true;
  }

  isRouteActive(route?: string): boolean {
    if (!route) return false;

    const currentUrl = this.router.url;

    if (route === '/dashboard') {
      return currentUrl === '/dashboard' || currentUrl === '/';
    }

    const isActive = currentUrl.startsWith(route);

    return isActive;
  }

  onNavItemClick(item: NavigationItem): void {
    if (item.action) {
      item.action();
    } else if (item.route) {
      this.navigateTo(item.route);
    }
  }
}
