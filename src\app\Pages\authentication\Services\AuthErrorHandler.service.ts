import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NotificationService } from '../../../core/Services/Notification.service';

@Injectable({
  providedIn: 'root',
})
export class AuthErrorHandlerService {
  constructor(private notificationService: NotificationService) {}

  // public handleError(error: HttpErrorResponse) {
  //   const apiError = error.error;
  //   const errorMessage = apiError?.message || 'An unexpected error occurred';

  //   this.notificationService.showError('Error', errorMessage);
  //   return throwError(() => errorMessage);
  // }
}
