import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthService } from './Pages/authentication/Services/Auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  private authSubscription: Subscription = new Subscription();

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authSubscription = this.authService.isAuthenticated$.subscribe(
      (isAuth) => {
        this.isAuthenticated = isAuth;
      },
    );
  }

  ngOnDestroy(): void {
    this.authSubscription.unsubscribe();
  }
}
