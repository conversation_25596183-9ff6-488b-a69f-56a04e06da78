import {
  Directive,
  Input,
  TemplateRef,
  ViewContainerRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Directive({
  standalone: false,
  selector: '[appAdminAccess]',
})
export class AdminAccessDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.checkAdminAccess();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkAdminAccess(): void {
    const userRoles = this.authService.getUserRoles();
    const hasAdminAccess = this.hasAdminRole(userRoles);

    if (hasAdminAccess && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasAdminAccess && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private hasAdminRole(roles: string[]): boolean {
    if (!roles || roles.length === 0) return false;

    // Check if user has only "user" role (case-insensitive)
    const hasOnlyUserRole =
      roles.length === 1 && roles[0].toLowerCase() === 'user';

    // If user has only "user" role, they don't have admin access
    if (hasOnlyUserRole) {
      return false;
    }

    // Check for admin or super admin roles (case-insensitive)
    const hasAdminRole = roles.some(
      (role) =>
        role.toLowerCase() === 'admin' ||
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    return hasAdminRole;
  }
}
