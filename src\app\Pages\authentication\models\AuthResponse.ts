export interface ApiResponse<T = any> {
  isSuccess: boolean;
  message: string;
  token?: string;
  data?: T;
}

export interface AuthData {
  requiresOtp?: boolean;
  userId?: string;
}

export interface AuthResponse extends ApiResponse<AuthData> {}

export type LoginResponse = AuthResponse;
export type OtpVerificationResponse = AuthResponse;
export type ForgotPasswordResponse = ApiResponse;
export type ResetPasswordResponse = ApiResponse;
export type VerifyResetTokenResponse = ApiResponse;
